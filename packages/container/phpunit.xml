<?xml version="1.0" encoding="UTF-8"?>
<phpunit
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/11.4/phpunit.xsd"
	bootstrap="vendor/autoload.php"
	executionOrder="depends,defects"
	beStrictAboutOutputDuringTests="true"
	displayDetailsOnPhpunitDeprecations="true"
	failOnPhpunitDeprecation="false"
	failOnRisky="true"
	failOnWarning="true"
>
	<testsuites>
		<testsuite name="Tempest Container">
			<directory>tests</directory>
		</testsuite>
	</testsuites>
	<source restrictNotices="true" restrictWarnings="true" ignoreIndirectDeprecations="true">
		<include>
			<directory>src</directory>
		</include>
	</source>
</phpunit>
