{"name": "tempest/event-bus", "description": "A lightweight event bus component designed to facilitate event-driven architecture and asynchronous message handling.", "require": {"php": "^8.4", "tempest/core": "dev-main", "tempest/container": "dev-main", "tempest/reflection": "dev-main"}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Tempest\\EventBus\\": "src"}}, "autoload-dev": {"psr-4": {"Tempest\\EventBus\\Tests\\": "tests"}}, "license": "MIT", "minimum-stability": "dev"}