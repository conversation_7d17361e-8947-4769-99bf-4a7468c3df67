<?php

namespace App;

use Tempest\Generation\Tests\Fixtures\Database\FakeCreateTableStatement;
use Tempest\Generation\Tests\Fixtures\Database\FakeQueryStatement;
use Tempest\Generation\Tests\Fixtures\Database\MigrationModel;

class CreateUsersTable
{
    public function up(): FakeQueryStatement
    {
        return (new FakeCreateTableStatement(MigrationModel::table()))
                ->primary()
                ->text('name');
    }
}
