<?php

namespace Tempest\Generation\Tests\Fixtures;

use Tempest\Generation\Tests\Fixtures\Database\FakeCreateTableStatement;
use Tempest\Generation\Tests\Fixtures\Database\FakeMigration;
use Tempest\Generation\Tests\Fixtures\Database\FakeQueryStatement;
use Tempest\Generation\Tests\Fixtures\Database\MigrationModel;

use function Tempest\Database\model;

#[TestAttribute]
final readonly class CreateMigrationsTable implements FakeMigration
{
    public function getName(): string
    {
        return '0000-00-00_create_migrations_table';
    }

    public function up(): FakeQueryStatement
    {
        return new FakeCreateTableStatement(model(MigrationModel::class)->getTableDefinition()->name)
            ->primary()
            ->text('name');
    }
}
