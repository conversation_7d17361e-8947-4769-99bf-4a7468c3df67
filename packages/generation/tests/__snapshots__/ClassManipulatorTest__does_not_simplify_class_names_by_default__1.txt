<?php

namespace Tempest\Generation\Tests\Fixtures;

use Tempest\Generation\Tests\Fixtures\Database\FakeMigration;
use Tempest\Generation\Tests\Fixtures\Database\FakeQueryStatement;

#[TestAttribute]
final readonly class CreateMigrationsTable implements FakeMigration
{
    public function getName(): string
    {
        return '0000-00-00_create_migrations_table';
    }

    public function up(): FakeQueryStatement
    {
        return new Database\FakeCreateTableStatement(\Tempest\Database\model(Database\MigrationModel::class)->getTableDefinition()->name)
            ->primary()
            ->text('name');
    }
}
