{"name": "tempest/view", "description": "A component for view handling.", "license": "MIT", "minimum-stability": "dev", "require": {"php": "^8.4", "tempest/core": "dev-main", "tempest/container": "dev-main", "tempest/validation": "dev-main", "tempest/clock": "dev-main", "league/commonmark": "^2.7", "symfony/cache": "^7.2"}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Tempest\\View\\": "src"}}, "autoload-dev": {"psr-4": {"Tempest\\View\\Tests\\": "tests"}}}