<?php

declare(strict_types=1);

namespace Tempest\Rector;

use Tempest\Console\Console;
use Tempest\Console\ConsoleCommand;

final readonly class TestTempestUpgrade
{
    public function __construct(
        private Console $console,
    ) {}

    #[ConsoleCommand(name: 'upgrade:test', description: 'Upgrades the Tempest framework')]
    public function __invoke(): void
    {
        $this->console->writeln('Upgrading Tempest');
    }
}
