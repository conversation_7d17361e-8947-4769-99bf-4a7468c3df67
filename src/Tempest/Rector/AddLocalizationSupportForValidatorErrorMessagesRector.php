<?php

declare(strict_types=1);

namespace Tempest\Rector;

use InvalidArgumentException;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Node;
use Php<PERSON><PERSON><PERSON>\Node\Name;
use <PERSON>\Contract\Rector\ConfigurableRectorInterface;
use <PERSON>\Rector\AbstractRector;

use function is_string;

/**
 * Transforms validation rule attributes based on configured mappings.
 *
 * Example configuration:
 * ```php
 * ->withConfiguredRule(AddLocalizationSupportForValidatorErrorMessagesRector::class, [
 *     'validationRuleMapping' => [
 *         'AfterDate' => 'IsAfterDate',
 *         'BeforeDate' => 'IsBeforeDate',
 *     ],
 * ])
 * ```
 *
 * @see \Tests\Rector\AddLocalizationSupportForValidatorErrorMessagesRector\AddLocalizationSupportForValidatorErrorMessagesRectorTest
 */
final class AddLocalizationSupportForValidatorErrorMessagesRector extends AbstractRector implements ConfigurableRectorInterface
{
    /**
     * @var array<string, string>
     */
    private array $validationRuleMapping = [];

    public function configure(array $configuration): void
    {
        $this->validationRuleMapping = $configuration['validationRuleMapping'] ?? [];
        foreach ($this->validationRuleMapping as $oldName => $newName) {
            if (! is_string($oldName) || ! is_string($newName)) {
                throw new InvalidArgumentException('Attribute mappings must be string to string mappings');
            }

            if (empty($oldName) || empty($newName)) {
                throw new InvalidArgumentException('Attribute names cannot be empty');
            }
        }
    }

    /**
     * @return array<class-string<Node>>
     */
    public function getNodeTypes(): array
    {
        return [\PhpParser\Node\Stmt\Property::class];
    }

    /**
     * @param \PhpParser\Node\Stmt\Property $node
     */
    public function refactor(Node $node): ?Node
    {
        $hasChanged = false;

        // Check if we have any attribute groups
        if (empty($node->attrGroups)) {
            return null;
        }

        // Check if we have any configured mappings
        if (empty($this->validationRuleMapping)) {
            return null;
        }

        // Iterate through all attribute groups on this property
        foreach ($node->attrGroups as $attrGroup) {
            // Iterate through all attributes in this group
            foreach ($attrGroup->attrs as $attribute) {
                if ($attribute->name instanceof Name) {
                    $attributeName = $attribute->name->toString();

                    // Check if this attribute name is in our configured mappings
                    if (isset($this->validationRuleMapping[$attributeName])) {
                        $newAttributeName = $this->validationRuleMapping[$attributeName];
                        $attribute->name = new Name($newAttributeName);
                        $hasChanged = true;
                    }
                }
            }
        }

        return $hasChanged ? $node : null;
    }
}
