<?php

declare(strict_types=1);

namespace Tempest\Rector;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Node;
use <PERSON>\Rector\AbstractRector;

/**
 * @see \Tests\Rector\AddLocalizationSupportForValidatorErrorMessagesRector\AddLocalizationSupportForValidatorErrorMessagesRectorTest
 */
final class AddLocalizationSupportForValidatorErrorMessagesRector extends AbstractRector
{
    /**
     * @return array<class-string<Node>>
     */
    public function getNodeTypes(): array
    {
        return [\PhpParser\Node\Stmt\Property::class];
    }

    /**
     * @param \PhpParser\Node\Stmt\Property $node
     */
    public function refactor(Node $node): ?Node
    {
        $hasChanged = false;

        // Check if we have any attribute groups
        if (empty($node->attrGroups)) {
            return null;
        }

        // Iterate through all attribute groups on this property
        foreach ($node->attrGroups as $attrGroup) {
            // Iterate through all attributes in this group
            foreach ($attrGroup->attrs as $attribute) {
                // Check if this attribute is named "AfterDate"
                if ($attribute->name instanceof \PhpParser\Node\Name && $attribute->name->toString() === 'AfterDate') {
                    // Replace with "IsAfterDate"
                    $attribute->name = new \PhpParser\Node\Name('IsAfterDate');
                    $hasChanged = true;
                }
            }
        }

        return $hasChanged ? $node : null;
    }
}
