<?php

declare(strict_types=1);

namespace Tempest\Rector;

use Php<PERSON><PERSON>er\Builder\Class_;
use Php<PERSON><PERSON><PERSON>\Node;
use PhpParser\Node\Name;
use PhpParser\Node\UseItem;
use <PERSON>\Rector\AbstractRector;
use function Tempest\Support\arr;

/**
 * @see \Tests\Rector\AddLocalizationSupportForValidatorErrorMessagesRector\AddLocalizationSupportForValidatorErrorMessagesRectorTest
 */
final class AddLocalizationSupportForValidatorErrorMessagesRector extends AbstractRector
{
    /**
     * @return array<class-string<Node>>
     */
    public function getNodeTypes(): array
    {
        return [\PhpParser\Node\Stmt\PropertyProperty::class];
    }

    /**
     * @param \PhpParser\Node\Stmt\PropertyProperty $node
     */
    public function refactor(Node $node): ?Node
    {
        $scope = $node->getAttribute('scope');
        dd(($scope));
        return $node;
    }
}
