<?php

namespace Tests\Rector\AddLocalizationSupportForValidatorErrorMessagesRector\Fixture;

final class BookRequest implements Request
{
    use IsRequest;

    #[AfterDate(date: '2024-01-10')]
    public DateTime $date;
}
?>
-----
<?php

namespace Tests\Rector\AddLocalizationSupportForValidatorErrorMessagesRector\Fixture;

final class BookRequest implements Request
{
    use IsRequest;

    #[IsAfterDate(date: '2024-01-10')]
    public DateTime $date;
}
?>
