#!/usr/bin/env php
<?php

declare(strict_types=1);

namespace Tests\Rector;

use Exception;
use InvalidArgumentException;
use RecursiveDirectoryIterator;
use RecursiveIteratorIterator;
use RuntimeException;
use Symfony\Component\Process\Process;
use Tempest\Console\Console;

use function file_exists;
use function file_exists as file_exists1;
use function file_exists as file_exists2;
use function is_dir;

final class RectorTestRunner
{
    private const string FIXTURE_SEPARATOR = '-----';
    private const string FIXTURE_EXTENSION = '.php.inc';

    private readonly string $rectorBinaryPath;
    private readonly string $rectorConfigPath;
    private array $testResults = [];

    public function __construct(
        private Console $console,
    ) {
        $projectRoot = dirname(__DIR__, 2);
        $this->rectorBinaryPath = $projectRoot . '/vendor/bin/rector';
        $this->rectorConfigPath = $projectRoot . '/src/Tempest/Rector/rector.upgrade.php';

        if (! file_exists2($this->rectorBinaryPath)) {
            throw new RuntimeException("Rector binary not found at: {$this->rectorBinaryPath}");
        }

        if (! file_exists2($this->rectorConfigPath)) {
            throw new RuntimeException("Rector config not found at: {$this->rectorConfigPath}");
        }
    }

    public function discoverFixtures(string $fixtureDirectory): array
    {
        if (! is_dir($fixtureDirectory)) {
            throw new InvalidArgumentException("Fixture directory not found: {$fixtureDirectory}");
        }

        $fixtureFiles = [];
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($fixtureDirectory, RecursiveDirectoryIterator::SKIP_DOTS),
        );

        foreach ($iterator as $file) {
            if ($file->getExtension() === 'inc' && str_ends_with($file->getFilename(), self::FIXTURE_EXTENSION)) {
                $fixtureFiles[] = $file->getPathname();
            }
        }

        sort($fixtureFiles);
        return $fixtureFiles;
    }

    public function parseFixture(string $fixturePath): array
    {
        if (! file_exists1($fixturePath)) {
            throw new InvalidArgumentException("Fixture file not found: {$fixturePath}");
        }

        $content = file_get_contents($fixturePath);
        $parts = explode(self::FIXTURE_SEPARATOR, $content, 2);

        if (count($parts) !== 2) {
            throw new InvalidArgumentException(
                "Invalid fixture format in {$fixturePath}. Expected '" . self::FIXTURE_SEPARATOR . "' separator.",
            );
        }

        return [
            'input' => trim($parts[0]),
            'expected' => trim($parts[1]),
            'filename' => basename($fixturePath),
        ];
    }

    public function runFixtureTest(array $fixture): array
    {
        $testName = $fixture['filename'];
        $this->console->writeln("📋 Testing fixture: {$testName}");

        $temporaryFile = $this->createTemporaryFile($fixture['input']);

        try {
            $processResult = $this->executeRectorProcess($temporaryFile);
            $testResult = $this->createTestResult($fixture, $processResult);

            if ($processResult['success']) {
                $transformedCode = file_get_contents($temporaryFile);
                return $this->evaluateTransformation($testResult, $transformedCode, $fixture['expected']);
            }

            return $this->handleProcessFailure($testResult, $processResult);
        } finally {
            $this->cleanupTemporaryFile($temporaryFile);
        }
    }

    private function createTemporaryFile(string $content): string
    {
        $tempFile = tempnam(sys_get_temp_dir(), 'rector_fixture_') . '.php';
        file_put_contents($tempFile, $content);

        return $tempFile;
    }

    private function executeRectorProcess(string $filePath): array
    {
        $command = "{$this->rectorBinaryPath} process {$filePath} --config={$this->rectorConfigPath} --no-ansi";
        $process = Process::fromShellCommandline($command);
        $process->run();

        $combinedOutput = $process->getOutput() . $process->getErrorOutput();
        $outputLines = array_filter(explode("\n", $combinedOutput));

        return [
            'success' => $process->getExitCode() === 0,
            'exitCode' => $process->getExitCode(),
            'output' => implode("\n", $outputLines),
        ];
    }

    private function createTestResult(array $fixture, array $processResult): array
    {
        return [
            'fixture' => $fixture['filename'],
            'success' => false,
            'expected' => $fixture['expected'],
            'actual' => '',
            'output' => $processResult['output'],
            'returnCode' => $processResult['exitCode'],
            'message' => '',
        ];
    }

    private function evaluateTransformation(array $testResult, string $transformedCode, string $expectedCode): array
    {
        $testResult['actual'] = trim($transformedCode);

        if ($this->isCodeTransformationCorrect($testResult['actual'], $expectedCode)) {
            $testResult['success'] = true;
            $testResult['message'] = '✅ Code transformation matches expected output';
            $this->console->success($testResult['message']);

            return $testResult;
        }

        $testResult['message'] = '❌ Code transformation does not match expected output';
        $this->console->error($testResult['message']);
        $this->displayCodeDifference($expectedCode, $testResult['actual']);

        return $testResult;
    }

    private function handleProcessFailure(array $testResult, array $processResult): array
    {
        $testResult['message'] = "❌ Rector failed with return code: {$processResult['exitCode']}";
        $this->console->error($testResult['message']);
        $this->console->writeln("Output: {$processResult['output']}");

        return $testResult;
    }

    private function cleanupTemporaryFile(string $filePath): void
    {
        if (file_exists($filePath)) {
            unlink($filePath);
        }
    }

    private function isCodeTransformationCorrect(string $actualCode, string $expectedCode): bool
    {
        return $this->normalizeCodeForComparison($actualCode) === $this->normalizeCodeForComparison($expectedCode);
    }

    private function normalizeCodeForComparison(string $code): string
    {
        $normalized = trim($code);
        $normalized = preg_replace('/\s+/', ' ', $normalized);
        $normalized = str_replace(['<?php', '?>'], '', $normalized);

        return trim($normalized);
    }

    private function displayCodeDifference(string $expectedCode, string $actualCode): void
    {
        $this->console->writeln("\n🔍 Expected:\n");
        $this->console->writeln("---\n{$expectedCode}\n---\n");
        $this->console->writeln("\n🔍 Actual:\n");
        $this->console->writeln("---\n{$actualCode}\n---\n");
    }

    public function runAllFixtures(string $fixtureDirectory): array
    {
        $this->displayTestHeader($fixtureDirectory);

        $fixtureFiles = $this->discoverFixtures($fixtureDirectory);

        if ($this->hasNoFixtures($fixtureFiles, $fixtureDirectory)) {
            return [];
        }

        $this->displayFixtureCount($fixtureFiles);

        return $this->processAllFixtures($fixtureFiles);
    }

    private function displayTestHeader(string $fixtureDirectory): void
    {
        $this->console->writeln("🧪 Running comprehensive Rector rule tests...\n");
        $this->console->writeln("📁 Fixture directory: {$fixtureDirectory}\n");
        $this->console->writeln("⚙️  Rector config: {$this->rectorConfigPath}\n");
    }

    private function hasNoFixtures(array $fixtureFiles, string $fixtureDirectory): bool
    {
        if (empty($fixtureFiles)) {
            $this->console->warning("⚠️  No fixture files found in {$fixtureDirectory}\n");
            return true;
        }
        return false;
    }

    private function displayFixtureCount(array $fixtureFiles): void
    {
        $this->console->writeln('📝 Found ' . count($fixtureFiles) . " fixture file(s)\n");
    }

    private function processAllFixtures(array $fixtureFiles): array
    {
        $results = [];

        foreach ($fixtureFiles as $fixturePath) {
            $result = $this->processSingleFixture($fixturePath);
            $results[] = $result;
            $this->testResults[] = $result;
        }

        return $results;
    }

    private function processSingleFixture(string $fixturePath): array
    {
        try {
            $fixture = $this->parseFixture($fixturePath);
            return $this->runFixtureTest($fixture);
        } catch (Exception $exception) {
            return $this->createErrorResult($fixturePath, $exception);
        }
    }

    private function createErrorResult(string $fixturePath, Exception $exception): array
    {
        $errorResult = [
            'fixture' => basename($fixturePath),
            'success' => false,
            'message' => '❌ Error processing fixture: ' . $exception->getMessage(),
            'expected' => '',
            'actual' => '',
            'output' => '',
            'returnCode' => -1,
        ];

        $this->console->error($errorResult['message'] . "\n");
        return $errorResult;
    }

    public function generateReport(): void
    {
        $statistics = $this->calculateTestStatistics();

        $this->displayReportHeader();
        $this->displayTestStatistics($statistics);
        $this->displayFailedTests($statistics['failed']);
        $this->displayReportSummary($statistics['failed']);
    }

    private function calculateTestStatistics(): array
    {
        $total = count($this->testResults);
        $passed = count(array_filter($this->testResults, fn ($result) => $result['success']));
        $failed = $total - $passed;
        $successRate = $total > 0 ? round(($passed / $total) * 100, 1) : 0;

        return compact('total', 'passed', 'failed', 'successRate');
    }

    private function displayReportHeader(): void
    {
        $this->console->writeln("\n" . str_repeat('=', 60));
        $this->console->writeln('📊 TEST REPORT');
        $this->console->writeln(str_repeat('=', 60));
    }

    private function displayTestStatistics(array $statistics): void
    {
        $this->console->writeln("Total tests: {$statistics['total']}");
        $this->console->writeln("Passed: {$statistics['passed']} ✅");
        $this->console->writeln("Failed: {$statistics['failed']} ❌");
        $this->console->writeln("Success rate: {$statistics['successRate']}%");
    }

    private function displayFailedTests(int $failedCount): void
    {
        if ($failedCount === 0) {
            return;
        }

        $this->console->writeln("\n🔍 Failed tests:");
        foreach ($this->testResults as $result) {
            if (! $result['success']) {
                $this->console->writeln("  - {$result['fixture']}: {$result['message']}");
            }
        }
    }

    private function displayReportSummary(int $failedCount): void
    {
        $message = $failedCount === 0 ? '🎉 All tests passed!' : '❌ Some tests failed.';
        $this->console->writeln("\n{$message}");
    }

    public function allTestsPassed(): bool
    {
        return empty(array_filter($this->testResults, fn ($result) => ! $result['success']));
    }
}
