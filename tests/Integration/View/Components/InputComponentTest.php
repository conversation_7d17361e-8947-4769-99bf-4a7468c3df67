<?php

namespace Integration\View\Components;

use Tempest\Http\Session\Session;
use Tempest\Validation\Rules\HasLength;
use Tempest\Validation\Rules\IsInteger;
use Tempest\Validation\Rules\IsString;
use Tempest\Validation\Validator;
use Tests\Tempest\Integration\FrameworkIntegrationTestCase;

final class InputComponentTest extends FrameworkIntegrationTestCase
{
    public function test_simple_input(): void
    {
        $html = $this->render('<x-input name="name" />');

        $this->assertStringContainsString('<label for="name">Name</label>', $html);
        $this->assertStringContainsString('<input type="text" name="name" id="name"', $html);
    }

    public function test_with_label(): void
    {
        $html = $this->render('<x-input name="name" label="Test" />');

        $this->assertStringContainsString('<label for="name">Test</label>', $html);
    }

    public function test_with_id(): void
    {
        $html = $this->render('<x-input name="name" id="test" />');

        $this->assertStringContainsString('<label for="test">', $html);
        $this->assertStringContainsString('id="test"', $html);
    }

    public function test_with_type(): void
    {
        $html = $this->render('<x-input name="name" type="email" />');

        $this->assertStringContainsString('type="email"', $html);
    }

    public function test_input_original(): void
    {
        $this->get(Session::class)->set(Session::ORIGINAL_VALUES, [
            'name' => 'original',
            'other' => 'other',
        ]);

        $html = $this->render('<x-input name="name" />');

        $this->assertStringContainsString('value="original"', $html);
    }

    public function test_textarea(): void
    {
        $html = $this->render('<x-input name="name" type="textarea" />');

        $this->assertStringContainsString('<textarea', $html);
        $this->assertStringNotContainsString('<input', $html);
    }

    public function test_textarea_original(): void
    {
        $this->get(Session::class)->set(Session::ORIGINAL_VALUES, [
            'name' => 'original',
            'other' => 'other',
        ]);

        $html = $this->render('<x-input name="name" type="textarea" />');

        $this->assertStringContainsString('>original</textarea>', $html);
    }

    public function test_error_message(): void
    {
        $failingRules = [
            'name' => [
                new IsString(),
                new HasLength(min: 5),
            ],
            'other' => [
                new IsInteger(),
            ],
        ];

        $this->get(Session::class)->set(Session::VALIDATION_ERRORS, $failingRules);

        $html = $this->render('<x-input name="name" />');

        $validator = $this->container->get(Validator::class);

        $this->assertStringContainsString($validator->getErrorMessage(new IsString()), $html);
        $this->assertStringContainsString($validator->getErrorMessage(new HasLength(min: 5)), $html);
        $this->assertStringNotContainsString($validator->getErrorMessage(new IsInteger()), $html);
    }
}
